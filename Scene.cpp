#include "Scene.h"
#include <iostream>
#include <algorithm>

Scene::Scene() {
}

Scene::~Scene() {
}

void Scene::createDefaultScene() {
    clear();
    
    // Create ground plane
    createGroundPlane();
    
    // Setup default lighting
    setupDefaultLighting();
    
    std::cout << "Default scene created with ground plane and directional light" << std::endl;
}

void Scene::clear() {
    objects.clear();
    lights.clear();
}

SceneObject* Scene::addObject(const std::string& name) {
    auto object = std::make_unique<SceneObject>(name);
    SceneObject* ptr = object.get();
    objects.push_back(std::move(object));
    return ptr;
}

SceneObject* Scene::getObject(const std::string& name) {
    auto it = std::find_if(objects.begin(), objects.end(),
        [&name](const std::unique_ptr<SceneObject>& obj) {
            return obj->name == name;
        });
    
    return (it != objects.end()) ? it->get() : nullptr;
}

void Scene::removeObject(const std::string& name) {
    objects.erase(
        std::remove_if(objects.begin(), objects.end(),
            [&name](const std::unique_ptr<SceneObject>& obj) {
                return obj->name == name;
            }),
        objects.end());
}

void Scene::addLight(const Light& light) {
    lights.push_back(light);
}

void Scene::removeLight(size_t index) {
    if (index < lights.size()) {
        lights.erase(lights.begin() + index);
    }
}

Light* Scene::getLight(size_t index) {
    return (index < lights.size()) ? &lights[index] : nullptr;
}

void Scene::update(float deltaTime) {
    // Update all objects (animations, etc.)
    for (auto& object : objects) {
        if (object->model) {
            object->model->update(deltaTime);
        }
    }
}

bool Scene::initializeVulkanResources(VkDevice device, VkPhysicalDevice physicalDevice, 
                                     VkCommandPool commandPool, VkQueue graphicsQueue) {
    for (auto& object : objects) {
        if (object->model) {
            for (auto& mesh : object->model->meshes) {
                if (!mesh.createBuffers(device, physicalDevice, commandPool, graphicsQueue)) {
                    std::cerr << "Failed to create buffers for mesh in object: " << object->name << std::endl;
                    return false;
                }
            }
            
            // Initialize animation system
            object->model->initializeAnimationSystem();
        }
    }
    
    return true;
}

void Scene::cleanup(VkDevice device) {
    for (auto& object : objects) {
        if (object->model) {
            object->model->cleanup(device);
        }
    }
}

void Scene::createGroundPlane() {
    auto groundObject = addObject("GroundPlane");
    groundObject->model = createPlaneModel(20.0f, 20.0f, 10);
    groundObject->setPosition(glm::vec3(0.0f, -1.0f, 0.0f));
    groundObject->setRotation(glm::vec3(-90.0f, 0.0f, 0.0f)); // Rotate to be horizontal
}

void Scene::setupDefaultLighting() {
    // Add main directional light (sun) - more from above
    Light mainLight;
    mainLight.type = LightType::DIRECTIONAL;
    mainLight.direction = glm::normalize(glm::vec3(-0.2f, -1.0f, -0.3f)); // More downward
    mainLight.color = glm::vec3(1.0f, 0.95f, 0.8f); // Warm sunlight
    mainLight.intensity = 1.5f; // Increased intensity
    addLight(mainLight);

    // Add fill light (opposite direction, weaker)
    Light fillLight;
    fillLight.type = LightType::DIRECTIONAL;
    fillLight.direction = glm::normalize(glm::vec3(0.3f, -0.3f, 0.2f));
    fillLight.color = glm::vec3(0.6f, 0.7f, 0.9f); // Cool fill light
    fillLight.intensity = 0.4f; // Increased intensity
    addLight(fillLight);

    // Set ambient light - increased
    setAmbientLight(glm::vec3(0.25f, 0.25f, 0.3f));
}

std::unique_ptr<FBXModel> Scene::createPlaneModel(float width, float height, int subdivisions) {
    auto model = std::make_unique<FBXModel>();
    
    Mesh planeMesh;
    
    // Create vertices for a subdivided plane
    int verticesPerSide = subdivisions + 1;
    float stepX = width / subdivisions;
    float stepZ = height / subdivisions;
    
    // Generate vertices
    for (int z = 0; z <= subdivisions; z++) {
        for (int x = 0; x <= subdivisions; x++) {
            Vertex vertex;
            vertex.position = glm::vec3(
                -width/2.0f + x * stepX,
                0.0f,
                -height/2.0f + z * stepZ
            );
            vertex.normal = glm::vec3(0.0f, 1.0f, 0.0f);
            vertex.texCoord = glm::vec2(
                (float)x / subdivisions,
                (float)z / subdivisions
            );
            vertex.boneIds = glm::ivec4(-1);
            vertex.boneWeights = glm::vec4(0.0f);
            
            planeMesh.vertices.push_back(vertex);
        }
    }
    
    // Generate indices
    for (int z = 0; z < subdivisions; z++) {
        for (int x = 0; x < subdivisions; x++) {
            int topLeft = z * verticesPerSide + x;
            int topRight = topLeft + 1;
            int bottomLeft = (z + 1) * verticesPerSide + x;
            int bottomRight = bottomLeft + 1;
            
            // First triangle
            planeMesh.indices.push_back(topLeft);
            planeMesh.indices.push_back(bottomLeft);
            planeMesh.indices.push_back(topRight);
            
            // Second triangle
            planeMesh.indices.push_back(topRight);
            planeMesh.indices.push_back(bottomLeft);
            planeMesh.indices.push_back(bottomRight);
        }
    }
    
    // Set material properties for the ground - make it brighter
    planeMesh.material.ambient = glm::vec3(0.3f, 0.3f, 0.3f);
    planeMesh.material.diffuse = glm::vec3(0.8f, 0.8f, 0.8f);
    planeMesh.material.specular = glm::vec3(0.2f, 0.2f, 0.2f);
    planeMesh.material.shininess = 8.0f;
    
    model->meshes.push_back(std::move(planeMesh));
    
    // Create simple root node
    model->rootNode.name = "PlaneRoot";
    model->rootNode.transformation = glm::mat4(1.0f);
    model->rootNode.meshIndices.push_back(0);
    
    return model;
}
