@echo off
echo Copying SDL2.dll to Debug and Release directories...
echo.

REM Create directories if they don't exist
if not exist "build\bin\Debug" mkdir "build\bin\Debug"
if not exist "build\bin\Release" mkdir "build\bin\Release"

REM Copy SDL2.dll to both directories
if exist "C:\SDL2\lib\x64\SDL2.dll" (
    copy "C:\SDL2\lib\x64\SDL2.dll" "build\bin\Debug\" >nul
    if %errorLevel% equ 0 (
        echo ✓ SDL2.dll copied to Debug directory
    ) else (
        echo ✗ Failed to copy SDL2.dll to Debug directory
    )
    
    copy "C:\SDL2\lib\x64\SDL2.dll" "build\bin\Release\" >nul
    if %errorLevel% equ 0 (
        echo ✓ SDL2.dll copied to Release directory
    ) else (
        echo ✗ Failed to copy SDL2.dll to Release directory
    )
) else (
    echo ✗ SDL2.dll not found at C:\SDL2\lib\x64\SDL2.dll
    echo Please make sure SDL2 is installed correctly.
)

echo.
echo Done!
pause
