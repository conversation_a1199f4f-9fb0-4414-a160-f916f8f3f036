#version 450

layout(binding = 0) uniform UniformBufferObject {
    mat4 model;
    mat4 view;
    mat4 proj;
    mat4 bones[100];

    // Lighting data
    vec3 lightDirection;
    float lightIntensity;
    vec3 lightColor;
    float padding1;
    vec3 ambientLight;
    float padding2;
    vec3 cameraPosition;
    float padding3;
} ubo;

layout(location = 0) in vec3 fragColor;
layout(location = 1) in vec2 fragTexCoord;
layout(location = 2) in vec3 fragNormal;
layout(location = 3) in vec3 fragWorldPos;
layout(location = 4) in vec3 fragViewPos;

layout(location = 0) out vec4 outColor;

void main() {
    // Normalize inputs
    vec3 normal = normalize(fragNormal);
    vec3 lightDir = normalize(-ubo.lightDirection); // Light direction points towards light
    vec3 viewDir = normalize(ubo.cameraPosition - fragWorldPos);

    // Material properties - make them brighter
    vec3 materialDiffuse = fragColor;
    vec3 materialSpecular = vec3(0.2, 0.2, 0.2);
    float shininess = 32.0;

    // Ambient lighting - increase ambient light
    vec3 ambient = ubo.ambientLight * materialDiffuse;

    // Diffuse lighting
    float diff = max(dot(normal, lightDir), 0.0);
    vec3 diffuse = diff * ubo.lightColor * ubo.lightIntensity * materialDiffuse;

    // Specular lighting (Blinn-Phong)
    vec3 halfwayDir = normalize(lightDir + viewDir);
    float spec = pow(max(dot(normal, halfwayDir), 0.0), shininess);
    vec3 specular = spec * ubo.lightColor * ubo.lightIntensity * materialSpecular;

    // Combine all lighting components
    vec3 result = ambient + diffuse + specular;

    // Debug: Show normal as color (uncomment to debug normals)
    // result = normal * 0.5 + 0.5;

    // Debug: Show light direction influence (uncomment to debug lighting)
    // result = vec3(max(dot(normal, lightDir), 0.0));

    // Ensure minimum brightness
    result = max(result, vec3(0.1));

    // Add some fog effect based on distance (reduced effect)
    float distance = length(fragViewPos);
    float fogFactor = exp(-distance * 0.01);
    fogFactor = clamp(fogFactor, 0.3, 1.0); // Don't let fog make things too dark

    vec3 fogColor = vec3(0.7, 0.8, 0.9);
    result = mix(fogColor, result, fogFactor);

    outColor = vec4(result, 1.0);
}
