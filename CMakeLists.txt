cmake_minimum_required(VERSION 3.16)
project(VulkanSDLWindow)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Disable debug info generation to avoid PDB issues
if(WIN32)
    set(CMAKE_C_FLAGS_DEBUG "/Od /RTC1")
    set(CMAKE_CXX_FLAGS_DEBUG "/Od /RTC1")
endif()

# Find Vulkan
find_package(Vulkan REQUIRED)

# Find FBX SDK
set(FBX_SDK_ROOT "" CACHE PATH "Path to FBX SDK")
if(WIN32)
    # Try to find FBX SDK in common locations
    find_path(FBX_SDK_INCLUDE_DIR fbxsdk.h
        HINTS
        ${FBX_SDK_ROOT}
        $ENV{FBX_SDK_ROOT}
        PATH_SUFFIXES include
        PATHS
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.3.7/include"
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.3.6/include"
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.3.5/include"
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.3.4/include"
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.3.3/include"
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.3.2/include"
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.3.1/include"
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.3/include"
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.2/include"
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.1/include"
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.0/include"
    )

    find_library(FBX_SDK_LIBRARY
        NAMES libfbxsdk-md libfbxsdk
        HINTS
        ${FBX_SDK_ROOT}
        $ENV{FBX_SDK_ROOT}
        PATH_SUFFIXES lib/vs2022/x64/release lib/vs2019/x64/release lib/vs2017/x64/release
        PATHS
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.3.7/lib/vs2022/x64/release"
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.3.6/lib/vs2022/x64/release"
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.3.5/lib/vs2022/x64/release"
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.3.4/lib/vs2022/x64/release"
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.3.3/lib/vs2022/x64/release"
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.3.2/lib/vs2022/x64/release"
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.3.1/lib/vs2022/x64/release"
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.3/lib/vs2022/x64/release"
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.2/lib/vs2022/x64/release"
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.1/lib/vs2022/x64/release"
        "C:/Program Files/Autodesk/FBX/FBX SDK/2020.0/lib/vs2022/x64/release"
    )

    if(FBX_SDK_INCLUDE_DIR AND FBX_SDK_LIBRARY)
        set(FBX_SDK_FOUND TRUE)
        set(FBX_SDK_INCLUDE_DIRS ${FBX_SDK_INCLUDE_DIR})
        set(FBX_SDK_LIBRARIES ${FBX_SDK_LIBRARY})
    else()
        set(FBX_SDK_FOUND FALSE)
        message(WARNING "FBX SDK not found. FBX loading will be disabled.")
    endif()
endif()

# Find GLM (header-only library)
find_path(GLM_INCLUDE_DIR glm/glm.hpp
    HINTS
    $ENV{GLM_ROOT}
    PATH_SUFFIXES include
    PATHS
    /usr/local/include
    /usr/include
    /opt/local/include
    /sw/include
    "C:/glm"
    "C:/Program Files/glm"
    "C:/Program Files (x86)/glm"
)

if(GLM_INCLUDE_DIR)
    set(GLM_FOUND TRUE)
    message(STATUS "Found GLM: ${GLM_INCLUDE_DIR}")
else()
    set(GLM_FOUND FALSE)
    message(WARNING "GLM not found. Will try to download it.")
endif()

# Find SDL2
if(WIN32)
    # For Windows, try to find SDL2 using different methods
    set(SDL2_DIR "" CACHE PATH "Path to SDL2")

    # Try to find SDL2 in common locations
    find_path(SDL2_INCLUDE_DIR SDL.h
        HINTS
        ${SDL2_DIR}
        $ENV{SDL2_DIR}
        PATH_SUFFIXES include include/SDL2
        PATHS
        ~/Library/Frameworks
        /Library/Frameworks
        /usr/local/include/SDL2
        /usr/include/SDL2
        /sw/include/SDL2
        /opt/local/include/SDL2
        /opt/csw/include/SDL2
        /opt/include/SDL2
        /mingw64/include/SDL2
        /mingw32/include/SDL2
        "C:/SDL2/include/SDL2"
        "C:/SDL2/include"
        "C:/Program Files/SDL2/include"
        "C:/Program Files (x86)/SDL2/include"
    )

    find_library(SDL2_LIBRARY
        NAMES SDL2 SDL2main
        HINTS
        ${SDL2_DIR}
        $ENV{SDL2_DIR}
        PATH_SUFFIXES lib lib64 lib/x64 lib/x86
        PATHS
        ~/Library/Frameworks
        /Library/Frameworks
        /usr/local
        /usr
        /sw
        /opt/local
        /opt/csw
        /opt
        /mingw64
        /mingw32
        "C:/SDL2/lib/x64"
        "C:/SDL2/lib"
        "C:/Program Files/SDL2/lib"
        "C:/Program Files (x86)/SDL2/lib"
    )

    find_library(SDL2MAIN_LIBRARY
        NAMES SDL2main
        HINTS
        ${SDL2_DIR}
        $ENV{SDL2_DIR}
        PATH_SUFFIXES lib lib64 lib/x64 lib/x86
        PATHS
        ~/Library/Frameworks
        /Library/Frameworks
        /usr/local
        /usr
        /sw
        /opt/local
        /opt/csw
        /opt
        /mingw64
        /mingw32
        "C:/SDL2/lib/x64"
        "C:/SDL2/lib"
        "C:/Program Files/SDL2/lib"
        "C:/Program Files (x86)/SDL2/lib"
    )

    if(SDL2_INCLUDE_DIR AND SDL2_LIBRARY)
        set(SDL2_FOUND TRUE)
        set(SDL2_INCLUDE_DIRS ${SDL2_INCLUDE_DIR})
        set(SDL2_LIBRARIES ${SDL2_LIBRARY})
        if(SDL2MAIN_LIBRARY)
            list(APPEND SDL2_LIBRARIES ${SDL2MAIN_LIBRARY})
        endif()
    else()
        set(SDL2_FOUND FALSE)
        message(WARNING "SDL2 not found. Please install SDL2 or set SDL2_DIR to the SDL2 installation directory.")
    endif()
else()
    # For non-Windows systems, try pkg-config first
    find_package(PkgConfig)
    if(PKG_CONFIG_FOUND)
        pkg_check_modules(SDL2 sdl2)
    endif()

    # If pkg-config didn't work, try find_package
    if(NOT SDL2_FOUND)
        find_package(SDL2)
    endif()
endif()

# Create executable
add_executable(${PROJECT_NAME}
    main.cpp
    VulkanRenderer.cpp
    FBXLoader.cpp
    Mesh.cpp
    Animation.cpp
    FileDialog.cpp
    Scene.cpp
    Camera.cpp
    InputManager.cpp
)

# Include directories
target_include_directories(${PROJECT_NAME} PRIVATE
    ${Vulkan_INCLUDE_DIRS}
)

if(SDL2_FOUND)
    target_include_directories(${PROJECT_NAME} PRIVATE ${SDL2_INCLUDE_DIRS})
endif()

if(FBX_SDK_FOUND)
    target_include_directories(${PROJECT_NAME} PRIVATE ${FBX_SDK_INCLUDE_DIRS})
    target_compile_definitions(${PROJECT_NAME} PRIVATE FBXSDK_SHARED)
endif()

if(GLM_FOUND)
    target_include_directories(${PROJECT_NAME} PRIVATE ${GLM_INCLUDE_DIR})
endif()

# Link libraries
target_link_libraries(${PROJECT_NAME}
    ${Vulkan_LIBRARIES}
)

if(SDL2_FOUND)
    target_link_libraries(${PROJECT_NAME} ${SDL2_LIBRARIES})
endif()

if(FBX_SDK_FOUND)
    target_link_libraries(${PROJECT_NAME} ${FBX_SDK_LIBRARIES})
endif()

# Platform-specific settings
if(WIN32)
    # If SDL2 is not found, provide instructions
    if(NOT SDL2_FOUND)
        message(FATAL_ERROR "SDL2 not found! Please download SDL2 development libraries from https://www.libsdl.org/download-2.0.php and extract to C:/SDL2/ or set SDL2_DIR")
    endif()
endif()

# Set output directory
set_target_properties(${PROJECT_NAME} PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Copy SDL2.dll to output directory for Windows
if(WIN32 AND SDL2_FOUND)
    # Find SDL2.dll
    find_file(SDL2_DLL
        NAMES SDL2.dll
        HINTS
        ${SDL2_DIR}
        $ENV{SDL2_DIR}
        PATH_SUFFIXES lib lib64 lib/x64 lib/x86 bin
        PATHS
        "C:/SDL2/lib/x64"
        "C:/SDL2/lib"
        "C:/SDL2/bin"
        "C:/Program Files/SDL2/lib/x64"
        "C:/Program Files/SDL2/lib"
        "C:/Program Files/SDL2/bin"
        "C:/Program Files (x86)/SDL2/lib/x64"
        "C:/Program Files (x86)/SDL2/lib"
        "C:/Program Files (x86)/SDL2/bin"
    )

    if(SDL2_DLL)
        message(STATUS "Found SDL2.dll: ${SDL2_DLL}")
        # Copy SDL2.dll to both Debug and Release directories
        add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
            "${SDL2_DLL}"
            "$<TARGET_FILE_DIR:${PROJECT_NAME}>/SDL2.dll"
            COMMENT "Copying SDL2.dll to output directory"
        )
    else()
        message(WARNING "SDL2.dll not found! The executable may not run without it.")
    endif()
endif()

# Copy shader files to output directory
add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E make_directory "$<TARGET_FILE_DIR:${PROJECT_NAME}>/shaders"
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${CMAKE_SOURCE_DIR}/shaders/vert.spv"
    "$<TARGET_FILE_DIR:${PROJECT_NAME}>/shaders/vert.spv"
    COMMAND ${CMAKE_COMMAND} -E copy_if_different
    "${CMAKE_SOURCE_DIR}/shaders/frag.spv"
    "$<TARGET_FILE_DIR:${PROJECT_NAME}>/shaders/frag.spv"
    COMMENT "Copying shader files to output directory"
)
