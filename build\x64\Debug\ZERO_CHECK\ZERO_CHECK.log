﻿  1>Checking Build System
  CMake is re-running because generate.stamp.list is missing.
  -- Found GLM: C:/glm
  CMake Warning at CMakeLists.txt:59 (message):
    FBX SDK not found.  FBX loading will be disabled.
  
  
  CMake Warning at CMakeLists.txt:166 (message):
    SDL2 not found.  Please install SDL2 or set SDL2_DIR to the SDL2
    installation directory.
  
  
  CMake Error at CMakeLists.txt:229 (message):
  -- Configuring incomplete, errors occurred!
    SDL2 not found! Please download SDL2 development libraries from
    https://www.libsdl.org/download-2.0.php and extract to C:/SDL2/ or set
    SDL2_DIR
  
  
  CMake Configure step failed.  Build files cannot be regenerated correctly.  Attempting to stop IDE build.
  系统找不到指定的批处理标签 - VCEnd
C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.CppCommon.targets(237,5): error MSB8066: “C:\Users\<USER>\Desktop\animation\build\CMakeFiles\d3201bc9dbfbfdacece2b7692677dc84\generate.stamp.rule”的自定义生成已退出，代码为 1。
